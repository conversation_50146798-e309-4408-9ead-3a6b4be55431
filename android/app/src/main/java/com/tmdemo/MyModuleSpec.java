package com.tmdemo;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableNativeMap;

import java.util.Map;
import java.util.HashMap;

/**
 * Abstract base class for MyModule TurboModule
 * This should normally be generated by React Native Codegen
 */
public abstract class MyModuleSpec extends ReactContextBaseJavaModule {
    
    public MyModuleSpec(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public abstract String getName();

    @ReactMethod
    public abstract void multiply(double a, double b, Promise promise);

    @ReactMethod
    public abstract void add(double a, double b, Promise promise);

    @Override
    public abstract Map<String, Object> getConstants();
}
