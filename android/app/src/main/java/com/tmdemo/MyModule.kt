package com.tmdemo

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.Promise

class MyModule(reactContext: ReactApplicationContext) : MyModuleSpec(reactContext) {

    override fun getName(): String = NAME

    // @function multiply
    override fun multiply(a: Double, b: Double, promise: Promise) {
        try {
            val result = a * b
            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("MULTIPLY_ERROR", "Failed to multiply numbers", e)
        }
    }

    // @function add
    override fun add(a: Double, b: Double, promise: Promise) {
        try {
            val result = a + b
            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("ADD_ERROR", "Failed to add numbers", e)
        }
    }

    // @function getConstants
    override fun getConstants(): Map<String, Any> {
        return mapOf("PI" to Math.PI)
    }

    companion object {
        const val NAME = "MyModule"
    }
}