package com.tmdemo

import com.mymodule.MyModuleSpec
import com.facebook.react.bridge.ReactApplicationContext

class MyModule(reactContext: ReactApplicationContext) : MyModuleSpec(reactContext) {
    fun getName() = NAME

    // @function multiply
    fun multiply(a: Int, b: Int): Int {
        return a * b
    }

    // @function add
    fun add(a: Int, b: Int): Promise<Int> {
        return Promise.resolve(a + b)
    }

    // @function getConstants
    fun getConstants(): Map<String, Any> {
        return mapOf("PI" to Math.PI)
    }

    companion object {
    const val NAME = "MyModule"
  }
}