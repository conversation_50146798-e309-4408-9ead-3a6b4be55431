package com.tmdemo

import com.facebook.react.BaseReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.model.ReactModuleInfo
import com.facebook.react.module.model.ReactModuleInfoProvider


class MyModulePackage : BaseReactPackage() {
    override fun getModule(name: String, reactContext: ReactApplicationContext): NativeModule? =
    if (name == MyModule.NAME) {
      MyModule(reactContext)
    } else {
      null
    }

    override fun getReactModuleInfoProvider() = ReactModuleInfoProvider {
        mapOf(
            MyModule.NAME to ReactModuleInfo(
                name = MyModule.NAME,
                className = MyModule.NAME,
                canOverrideExistingModule =false, // canOverrideExistingModule
                needsEagerInit = false, // needsEagerInit
                hasConstants = true, // hasConstants
                isCxxModule = false, // isCxxModule
                isTurboModule = true // isTurboModule
            )
        )
    }
}