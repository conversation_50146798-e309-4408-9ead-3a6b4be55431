import React from 'react';
import {View, StyleSheet, Text, TouchableOpacity} from 'react-native';
// import MyModule from './specs/MyModule';

// console.log('MyModule', MyModule);
function App(): React.JSX.Element {
  return (
    <View style={styles.container}>
      <Text>Hello World</Text>
      <TouchableOpacity style={styles.button}>
        <Text style={styles.buttonText}>Show Multipy Result</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.button}>
        <Text style={styles.buttonText}>Show Multipy Result</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
    gap: 16,
  },
  button: {
    backgroundColor: 'gray',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 4,
    shadowColor: 'black',
    shadowOpacity: 0.2,
    shadowRadius: 1,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    elevation: 7,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default App;
